import { pgTable, index, foreignKey, uuid, text, boolean, timestamp, integer, jsonb, unique, numeric, date } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const user = pgTable("user", {
	id: text("id").primaryKey(),
	name: text("name").notNull(),
	email: text("email").notNull().unique(),
	emailVerified: boolean("email_verified")
		.$defaultFn(() => false)
		.notNull(),
	image: text("image"),
	createdAt: timestamp("created_at")
		.$defaultFn(() => /* @__PURE__ */ new Date())
		.notNull(),
	updatedAt: timestamp("updated_at")
		.$defaultFn(() => /* @__PURE__ */ new Date())
		.notNull(),
	role: text("role"),
	banned: boolean("banned"),
	banReason: text("ban_reason"),
	banExpires: timestamp("ban_expires"),
},
	(table) => {
		return {
			idxUserEmail: index("idx_user_email").using("btree", table.email.asc().nullsLast()),
			idxUserCreatedAt: index("idx_user_created_at").using("btree", table.createdAt.asc().nullsLast()),
			idxUserRole: index("idx_user_role").using("btree", table.role.asc().nullsLast()),
		}
	});

export const session = pgTable("session", {
	id: text("id").primaryKey(),
	expiresAt: timestamp("expires_at").notNull(),
	token: text("token").notNull().unique(),
	createdAt: timestamp("created_at").notNull(),
	updatedAt: timestamp("updated_at").notNull(),
	ipAddress: text("ip_address"),
	userAgent: text("user_agent"),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	impersonatedBy: text("impersonated_by"),
	activeOrganizationId: text("active_organization_id"),
},
	(table) => {
		return {
			idxSessionUserId: index("idx_session_user_id").using("btree", table.userId.asc().nullsLast()),
			idxSessionToken: index("idx_session_token").using("btree", table.token.asc().nullsLast()),
			idxSessionExpiresAt: index("idx_session_expires_at").using("btree", table.expiresAt.asc().nullsLast()),
			idxSessionActiveOrganizationId: index("idx_session_active_organization_id").using("btree", table.activeOrganizationId.asc().nullsLast()),
		}
	});

export const account = pgTable("account", {
	id: text("id").primaryKey(),
	accountId: text("account_id").notNull(),
	providerId: text("provider_id").notNull(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	accessToken: text("access_token"),
	refreshToken: text("refresh_token"),
	idToken: text("id_token"),
	accessTokenExpiresAt: timestamp("access_token_expires_at"),
	refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
	scope: text("scope"),
	password: text("password"),
	createdAt: timestamp("created_at").notNull(),
	updatedAt: timestamp("updated_at").notNull(),
},
	(table) => {
		return {
			idxAccountUserId: index("idx_account_user_id").using("btree", table.userId.asc().nullsLast()),
			idxAccountProviderId: index("idx_account_provider_id").using("btree", table.providerId.asc().nullsLast()),
			idxAccountAccountId: index("idx_account_account_id").using("btree", table.accountId.asc().nullsLast()),
			idxAccountProviderUser: index("idx_account_provider_user").using("btree", table.providerId.asc().nullsLast(), table.userId.asc().nullsLast()),
		}
	});

export const verification = pgTable("verification", {
	id: text("id").primaryKey(),
	identifier: text("identifier").notNull(),
	value: text("value").notNull(),
	expiresAt: timestamp("expires_at").notNull(),
	createdAt: timestamp("created_at").$defaultFn(
		() => /* @__PURE__ */ new Date(),
	),
	updatedAt: timestamp("updated_at").$defaultFn(
		() => /* @__PURE__ */ new Date(),
	),
},
	(table) => {
		return {
			idxVerificationIdentifier: index("idx_verification_identifier").using("btree", table.identifier.asc().nullsLast()),
			idxVerificationExpiresAt: index("idx_verification_expires_at").using("btree", table.expiresAt.asc().nullsLast()),
			idxVerificationValue: index("idx_verification_value").using("btree", table.value.asc().nullsLast()),
		}
	});


export const listingNotes = pgTable("listing_notes", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	listingId: uuid("listing_id").notNull(),
	organizationId: text("organization_id").notNull(),
	createdBy: text("created_by").notNull(),
	content: text("content").notNull(),
	mentions: text("mentions").array().default([""]),
	isPrivate: boolean("is_private").default(false),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
},
	(table) => {
		return {
			idxListingNotesCreatedAt: index("idx_listing_notes_created_at").using("btree", table.createdAt.asc().nullsLast()),
			idxListingNotesCreatedBy: index("idx_listing_notes_created_by").using("btree", table.createdBy.asc().nullsLast()),
			idxListingNotesListingId: index("idx_listing_notes_listing_id").using("btree", table.listingId.asc().nullsLast()),
			idxListingNotesOrganizationId: index("idx_listing_notes_organization_id").using("btree", table.organizationId.asc().nullsLast()),
			listingNotesListingIdFkey: foreignKey({
				columns: [table.listingId],
				foreignColumns: [listings.id],
				name: "listing_notes_listing_id_fkey"
			}).onDelete("cascade"),
			listingNotesOrganizationIdFkey: foreignKey({
				columns: [table.organizationId],
				foreignColumns: [organization.id],
				name: "listing_notes_organization_id_fkey"
			}).onDelete("cascade"),
			listingNotesCreatedByFkey: foreignKey({
				columns: [table.createdBy],
				foreignColumns: [user.id],
				name: "listing_notes_created_by_fkey"
			}),
		}
	});

export const files = pgTable("files", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	organizationId: text("organization_id").notNull(),
	uploadedBy: text("uploaded_by").notNull(),
	fileName: text("file_name").notNull(),
	originalName: text("original_name").notNull(),
	mimeType: text("mime_type").notNull(),
	fileSize: integer("file_size").notNull(),
	storagePath: text("storage_path").notNull(),
	storageUrl: text("storage_url"),
	fileType: text("file_type").notNull(),
	entityType: text("entity_type"),
	entityId: uuid("entity_id"),
	isPublic: boolean("is_public").default(false),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
},
	(table) => {
		return {
			idxFilesCreatedAt: index("idx_files_created_at").using("btree", table.createdAt.asc().nullsLast()),
			idxFilesEntity: index("idx_files_entity").using("btree", table.entityType.asc().nullsLast(), table.entityId.asc().nullsLast()),
			idxFilesFileType: index("idx_files_file_type").using("btree", table.fileType.asc().nullsLast()),
			idxFilesUploadedBy: index("idx_files_uploaded_by").using("btree", table.uploadedBy.asc().nullsLast()),
			idxFilesOrganizationId: index("idx_files_organization_id").using("btree", table.organizationId.asc().nullsLast()),
			filesOrganizationIdFkey: foreignKey({
				columns: [table.organizationId],
				foreignColumns: [organization.id],
				name: "files_organization_id_fkey"
			}).onDelete("cascade"),
			filesUploadedByFkey: foreignKey({
				columns: [table.uploadedBy],
				foreignColumns: [user.id],
				name: "files_uploaded_by_fkey"
			}),
		}
	});


export const organization = pgTable("organization", {
	id: text("id").primaryKey(),
	name: text("name").notNull(),
	slug: text("slug").unique(),
	logo: text("logo"),
	createdAt: timestamp("created_at").notNull(),
	metadata: text("metadata"),
},
	(table) => {
		return {
			idxOrganizationSlug: index("idx_organization_slug").using("btree", table.slug.asc().nullsLast()),
			idxOrganizationCreatedAt: index("idx_organization_created_at").using("btree", table.createdAt.asc().nullsLast()),
			idxOrganizationName: index("idx_organization_name").using("btree", table.name.asc().nullsLast()),
		}
	});

export const member = pgTable("member", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
		.notNull()
		.references(() => organization.id, { onDelete: "cascade" }),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	role: text("role").default("member").notNull(),
	createdAt: timestamp("created_at").notNull(),
},
	(table) => {
		return {
			idxMemberOrganizationId: index("idx_member_organization_id").using("btree", table.organizationId.asc().nullsLast()),
			idxMemberUserId: index("idx_member_user_id").using("btree", table.userId.asc().nullsLast()),
			idxMemberRole: index("idx_member_role").using("btree", table.role.asc().nullsLast()),
			idxMemberOrgUser: index("idx_member_org_user").using("btree", table.organizationId.asc().nullsLast(), table.userId.asc().nullsLast()),
			uniqueMemberOrgUser: unique("unique_member_org_user").on(table.organizationId, table.userId),
		}
	});

export const invitation = pgTable("invitation", {
	id: text("id").primaryKey(),
	organizationId: text("organization_id")
		.notNull()
		.references(() => organization.id, { onDelete: "cascade" }),
	email: text("email").notNull(),
	role: text("role"),
	status: text("status").default("pending").notNull(),
	expiresAt: timestamp("expires_at").notNull(),
	inviterId: text("inviter_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
},
	(table) => {
		return {
			idxInvitationOrganizationId: index("idx_invitation_organization_id").using("btree", table.organizationId.asc().nullsLast()),
			idxInvitationEmail: index("idx_invitation_email").using("btree", table.email.asc().nullsLast()),
			idxInvitationStatus: index("idx_invitation_status").using("btree", table.status.asc().nullsLast()),
			idxInvitationExpiresAt: index("idx_invitation_expires_at").using("btree", table.expiresAt.asc().nullsLast()),
			idxInvitationInviterId: index("idx_invitation_inviter_id").using("btree", table.inviterId.asc().nullsLast()),
		}
	});

export const listings = pgTable("listings", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	organizationId: text("organization_id").notNull(),
	createdBy: text("created_by").notNull(),
	assignedTo: text("assigned_to"),
	// Core business listing fields
	businessName: text("business_name").notNull(),
	industry: text("industry").notNull(),
	askingPrice: numeric("asking_price", { precision: 12, scale: 2 }),
	cashFlowSde: numeric("cash_flow_sde", { precision: 12, scale: 2 }),
	annualRevenue: numeric("annual_revenue", { precision: 12, scale: 2 }),
	status: text("status").default('draft'), // Active, Under Contract, Sold, Confidential, etc.
	generalLocation: text("general_location"), // City/State/Region for confidentiality
	yearEstablished: integer("year_established"),
	employees: integer("employees"),
	ownerHoursWeek: integer("owner_hours_week"),
	dateListed: date("date_listed"),
	daysListed: integer("days_listed"), // Auto-calculated field
	// Legacy fields (keeping for compatibility)
	title: text("title"), // Can be used as alternate to businessName
	description: text("description"),
	price: numeric("price", { precision: 12, scale: 2 }), // Legacy - use askingPrice instead
	address: text("address"), // For internal use - not public
	city: text("city"),
	state: text("state"),
	zipCode: text("zip_code"),
	propertyType: text("property_type"),
	squareFootage: integer("square_footage"),
	lotSize: numeric("lot_size", { precision: 10, scale: 2 }),
	yearBuilt: integer("year_built"),
	bedrooms: integer("bedrooms"),
	bathrooms: numeric("bathrooms", { precision: 3, scale: 1 }),
	listingType: text("listing_type").default('business_sale'),
	teamVisibility: text("team_visibility").default('all'),
	internalNotes: jsonb("internal_notes").default([]),
	photos: text("photos").array(),
	documents: text("documents").array(),
	featuredPhoto: text("featured_photo"),
	virtualTourUrl: text("virtual_tour_url"),
	mlsNumber: text("mls_number"),
	listingDate: date("listing_date"), // Legacy - use dateListed instead
	expirationDate: date("expiration_date"),
	daysOnMarket: integer("days_on_market"), // Legacy - use daysListed instead
	// Draft data storage - stores incomplete listing data when status is 'draft'
	_draft: jsonb("_draft"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
},
	(table) => {
		return {
			idxListingsCreatedAt: index("idx_listings_created_at").using("btree", table.createdAt.asc().nullsLast()),
			idxListingsOrganizationId: index("idx_listings_organization_id").using("btree", table.organizationId.asc().nullsLast()),
			idxListingsStatus: index("idx_listings_status").using("btree", table.status.asc().nullsLast()),
			idxListingsIndustry: index("idx_listings_industry").using("btree", table.industry.asc().nullsLast()),
			idxListingsCreatedBy: index("idx_listings_created_by").using("btree", table.createdBy.asc().nullsLast()),
			idxListingsAssignedTo: index("idx_listings_assigned_to").using("btree", table.assignedTo.asc().nullsLast()),
			listingsOrganizationIdFkey: foreignKey({
				columns: [table.organizationId],
				foreignColumns: [organization.id],
				name: "listings_organization_id_fkey"
			}).onDelete("cascade"),
			listingsCreatedByFkey: foreignKey({
				columns: [table.createdBy],
				foreignColumns: [user.id],
				name: "listings_created_by_fkey"
			}),
			listingsAssignedToFkey: foreignKey({
				columns: [table.assignedTo],
				foreignColumns: [user.id],
				name: "listings_assigned_to_fkey"
			}),
		}
	});

export const listingDetails = pgTable("listing_details", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	listingId: uuid("listing_id").notNull(),
	// Business descriptions
	businessDescription: text("business_description"),
	briefDescription: text("brief_description"),
	// Financial details
	financialDetails: jsonb("financial_details").default({
		revenue_2023: null,
		ebitda: null,
		assets_included: [],
		inventory_value: null,
		additional_financial_info: {}
	}),
	// Operations
	operations: jsonb("operations").default({
		business_model: '',
		key_features: [],
		competitive_advantages: [],
		operational_details: {}
	}),
	// Growth and sale details
	growthOpportunities: text("growth_opportunities").array(),
	reasonForSale: text("reason_for_sale"),
	trainingPeriod: text("training_period"),
	supportType: text("support_type"),
	financingAvailable: boolean("financing_available").default(false),
	// Additional business details
	equipmentHighlights: text("equipment_highlights").array(),
	supplierRelationships: text("supplier_relationships"),
	realEstateStatus: text("real_estate_status"), // Owned, Leased, etc.
	leaseDetails: jsonb("lease_details").default({
		lease_terms: '',
		monthly_rent: null,
		lease_expiration: null,
		renewal_options: '',
		landlord_info: {}
	}),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
},
	(table) => {
		return {
			idxListingDetailsListingId: index("idx_listing_details_listing_id").using("btree", table.listingId.asc().nullsLast()),
			idxListingDetailsCreatedAt: index("idx_listing_details_created_at").using("btree", table.createdAt.asc().nullsLast()),
			listingDetailsListingIdFkey: foreignKey({
				columns: [table.listingId],
				foreignColumns: [listings.id],
				name: "listing_details_listing_id_fkey"
			}).onDelete("cascade"),
			uniqueListingDetails: unique("unique_listing_details").on(table.listingId),
		}
	});

export const listingStatusHistory = pgTable("listing_status_history", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	listingId: uuid("listing_id").notNull(),
	organizationId: text("organization_id").notNull(),
	changedBy: text("changed_by").notNull(),
	fromStatus: text("from_status"),
	toStatus: text("to_status").notNull(),
	reason: text("reason"),
	notes: text("notes"),
	metadata: jsonb("metadata").default({}),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
},
	(table) => {
		return {
			idxListingStatusHistoryListingId: index("idx_listing_status_history_listing_id").using("btree", table.listingId.asc().nullsLast()),
			idxListingStatusHistoryCreatedAt: index("idx_listing_status_history_created_at").using("btree", table.createdAt.asc().nullsLast()),
			idxListingStatusHistoryChangedBy: index("idx_listing_status_history_changed_by").using("btree", table.changedBy.asc().nullsLast()),
			idxListingStatusHistoryToStatus: index("idx_listing_status_history_to_status").using("btree", table.toStatus.asc().nullsLast()),
			idxListingStatusHistoryOrganizationId: index("idx_listing_status_history_organization_id").using("btree", table.organizationId.asc().nullsLast()),
			listingStatusHistoryListingIdFkey: foreignKey({
				columns: [table.listingId],
				foreignColumns: [listings.id],
				name: "listing_status_history_listing_id_fkey"
			}).onDelete("cascade"),
			listingStatusHistoryOrganizationIdFkey: foreignKey({
				columns: [table.organizationId],
				foreignColumns: [organization.id],
				name: "listing_status_history_organization_id_fkey"
			}).onDelete("cascade"),
			listingStatusHistoryChangedByFkey: foreignKey({
				columns: [table.changedBy],
				foreignColumns: [user.id],
				name: "listing_status_history_changed_by_fkey"
			}),
		}
	});

export const userProfiles = pgTable("user_profiles", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	displayName: text("display_name"),
	bio: text("bio"),
	isActive: boolean("is_active").default(true).notNull(),
	organizationId: text("organization_id"),
	firstName: text("first_name"),
	lastName: text("last_name"),
	phone: text("phone"),
	licenseNumber: text("license_number"),
	avatarUrl: text("avatar_url"),
	specialties: text("specialties").array(),
	invitedAt: timestamp("invited_at", { mode: 'string' }),
	joinedAt: timestamp("joined_at", { mode: 'string' }),
	invitedBy: text("invited_by"),
	userId: text("user_id"),
	preferences: jsonb("preferences").default({
		notifications: {
			email_notifications: true,
			push_notifications: true,
			listing_updates: true,
			team_updates: true,
			system_updates: true
		},
		display: {
			timezone: 'America/New_York',
			date_format: 'MM/DD/YYYY',
			currency: 'USD',
			language: 'en'
		},
		privacy: {
			profile_visibility: 'team',
			contact_visibility: 'team'
		}
	}),
	lastLoginAt: timestamp("last_login_at", { mode: 'string' }),
},
	(table) => {
		return {
			idxUserProfilesActive: index("idx_user_profiles_active").using("btree", table.isActive.asc().nullsLast()).where(sql`(is_active = true)`),
			idxUserProfilesUserId: index("idx_user_profiles_user_id").using("btree", table.userId.asc().nullsLast()),
			idxUserProfilesOrganizationId: index("idx_user_profiles_organization_id").using("btree", table.organizationId.asc().nullsLast()),
			userProfilesOrganizationIdFkey: foreignKey({
				columns: [table.organizationId],
				foreignColumns: [organization.id],
				name: "user_profiles_organization_id_fkey"
			}).onDelete("cascade"),
			userProfilesUserIdFkey: foreignKey({
				columns: [table.userId],
				foreignColumns: [user.id],
				name: "user_profiles_user_id_fkey"
			}).onDelete("cascade"),
			uniqueOrganizationUser: unique("unique_organization_user").on(table.organizationId, table.userId),
		}
	});

export const notifications = pgTable("notifications", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	organizationId: text("organization_id").notNull(),
	userId: text("user_id").notNull(),
	type: text("type").notNull(),
	title: text("title").notNull(),
	message: text("message").notNull(),
	data: jsonb("data").default({}),
	entityType: text("entity_type"),
	entityId: uuid("entity_id"),
	isRead: boolean("is_read").default(false),
	readAt: timestamp("read_at", { mode: 'string' }),
	priority: text("priority").default('normal'),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
},
	(table) => {
		return {
			idxNotificationsCreatedAt: index("idx_notifications_created_at").using("btree", table.createdAt.asc().nullsLast()),
			idxNotificationsEntity: index("idx_notifications_entity").using("btree", table.entityType.asc().nullsLast(), table.entityId.asc().nullsLast()),
			idxNotificationsIsRead: index("idx_notifications_is_read").using("btree", table.isRead.asc().nullsLast()),
			idxNotificationsPriority: index("idx_notifications_priority").using("btree", table.priority.asc().nullsLast()),
			idxNotificationsType: index("idx_notifications_type").using("btree", table.type.asc().nullsLast()),
			idxNotificationsUserId: index("idx_notifications_user_id").using("btree", table.userId.asc().nullsLast()),
			idxNotificationsOrganizationId: index("idx_notifications_organization_id").using("btree", table.organizationId.asc().nullsLast()),
			notificationsOrganizationIdFkey: foreignKey({
				columns: [table.organizationId],
				foreignColumns: [organization.id],
				name: "notifications_organization_id_fkey"
			}).onDelete("cascade"),
			notificationsUserIdFkey: foreignKey({
				columns: [table.userId],
				foreignColumns: [user.id],
				name: "notifications_user_id_fkey"
			}).onDelete("cascade"),
		}
	});

export const apiLogs = pgTable("_log", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	// Request information
	method: text("method").notNull(),
	url: text("url").notNull(),
	path: text("path").notNull(),
	userAgent: text("user_agent"),
	ipAddress: text("ip_address"),
	// User context
	userId: text("user_id"),
	organizationId: text("organization_id"),
	// Request data
	headers: jsonb("headers"),
	queryParams: jsonb("query_params"),
	requestBody: jsonb("request_body"),
	// Response data
	statusCode: integer("status_code"),
	responseBody: jsonb("response_body"),
	responseHeaders: jsonb("response_headers"),
	// Timing
	startTime: timestamp("start_time", { mode: 'string' }).notNull(),
	endTime: timestamp("end_time", { mode: 'string' }),
	duration: integer("duration"), // milliseconds
	// Error information
	errorMessage: text("error_message"),
	errorStack: text("error_stack"),
	// Metadata
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
},
	(table) => {
		return {
			idxApiLogsCreatedAt: index("idx_api_logs_created_at").using("btree", table.createdAt.desc().nullsLast()),
			idxApiLogsUserId: index("idx_api_logs_user_id").using("btree", table.userId.asc().nullsLast()),
			idxApiLogsOrganizationId: index("idx_api_logs_organization_id").using("btree", table.organizationId.asc().nullsLast()),
			idxApiLogsStatusCode: index("idx_api_logs_status_code").using("btree", table.statusCode.asc().nullsLast()),
			idxApiLogsMethod: index("idx_api_logs_method").using("btree", table.method.asc().nullsLast()),
			idxApiLogsPath: index("idx_api_logs_path").using("btree", table.path.asc().nullsLast()),
			// Composite indexes for optimized path-based queries
			idxApiLogsPathOrganization: index("idx_api_logs_path_organization").using("btree", table.path.asc().nullsLast(), table.organizationId.asc().nullsLast()),
			idxApiLogsPathCreatedAt: index("idx_api_logs_path_created_at").using("btree", table.path.asc().nullsLast(), table.createdAt.desc().nullsLast()),
			idxApiLogsPathMethod: index("idx_api_logs_path_method").using("btree", table.path.asc().nullsLast(), table.method.asc().nullsLast()),
			idxApiLogsPathStatus: index("idx_api_logs_path_status").using("btree", table.path.asc().nullsLast(), table.statusCode.asc().nullsLast()),
			// Foreign keys for user and organization if they exist
			apiLogsUserIdFkey: foreignKey({
				columns: [table.userId],
				foreignColumns: [user.id],
				name: "api_logs_user_id_fkey"
			}),
			apiLogsOrganizationIdFkey: foreignKey({
				columns: [table.organizationId],
				foreignColumns: [organization.id],
				name: "api_logs_organization_id_fkey"
			}).onDelete("cascade"),
		}
	});