import configureOpenAPI from "@/lib/configure-open-api";
import createApp from "@/lib/create-app";
import index from "@/routes/index.route";
import apiClient from "@/routes/api-client.route";
// import { authRouter } from "@/routes/v1/auth/auth.routes";
import { auth } from "@/lib/auth";
import { filesRouter } from "@/routes/v1/files/files.routes";
import { profilesRouter } from "@/routes/v1/profiles/profiles.routes";
import { listingsRouter } from "@/routes/v1/listings/listings.routes";
import { logsRouter } from "@/routes/v1/logs/logs.routes";
// import { workspacesRouter } from "@/routes/v1/workspaces/workspaces.routes";
// import { serveStatic } from "hono/serve-static";

const app = createApp();

configureOpenAPI(app);

// Mount Better Auth under /api/auth/*
app.all("/api/auth/*", async (c) => {
  return auth.handler(c.req.raw);
});

// Serve local uploads under /uploads/* from public/uploads
// app.use("/uploads/*", serveStatic({ root: "./public" }));

const routes = [
  index,
  apiClient,
  // authRouter,
  profilesRouter,
  filesRouter,
  listingsRouter,
  logsRouter,
  // workspacesRouter,
] as const;

routes.forEach((route) => {
  app.route("/", route);
});

export type AppType = typeof routes[number];

export default app;
