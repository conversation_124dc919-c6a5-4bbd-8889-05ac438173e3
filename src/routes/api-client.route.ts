import { createRoute } from "@hono/zod-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "@hono/zod-openapi";
import { readFileSync } from "node:fs";
import { join } from "node:path";

import { createRouter } from "@/lib/create-app";

// Response schema for the API client
const apiClientResponseSchema = z.object({
  version: z.string(),
  client: z.string(),
  types: z.string(),
  usage: z.object({
    installation: z.string(),
    basicUsage: z.string(),
    authentication: z.string(),
    examples: z.array(z.object({
      title: z.string(),
      code: z.string(),
    })),
  }),
}).openapi("ApiClientResponse");

// Generate API client code dynamically
function generateApiClientCode(): string {
  return `import type { paths, components } from "./api-types";

// Base API client configuration
export interface ApiClientConfig {
  baseUrl: string;
  timeout?: number;
  headers?: Record<string, string>;
}

// Default configuration
const DEFAULT_CONFIG: ApiClientConfig = {
  baseUrl: '${process.env.NODE_ENV === 'production' ? 'https://your-api-domain.com' : 'http://localhost:3001'}',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Generic response wrapper
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

// Error response type
export interface ApiError {
  message: string;
  status: number;
  details?: any;
}

export class ApiClient {
  protected config: ApiClientConfig;

  constructor(config: Partial<ApiClientConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // Helper method to make HTTP requests
  protected async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = \`\${this.config.baseUrl}\${endpoint}\`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.config.headers,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      let data: T;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = (await response.text()) as T;
      }

      if (!response.ok) {
        throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
      }

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
      };
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(\`Request timeout after \${this.config.timeout}ms\`);
        }
        throw error;
      }
      
      throw new Error('Unknown error occurred');
    }
  }

  // GET request
  async get<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'GET',
      headers,
    });
  }

  // POST request
  async post<T>(
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: body ? JSON.stringify(body) : undefined,
      headers,
    });
  }

  // PUT request
  async put<T>(
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: body ? JSON.stringify(body) : undefined,
      headers,
    });
  }

  // DELETE request
  async delete<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
      headers,
    });
  }

  // PATCH request
  async patch<T>(
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: body ? JSON.stringify(body) : undefined,
      headers,
    });
  }

  // Set authorization header
  setAuth(token: string): void {
    this.config.headers = {
      ...this.config.headers,
      Authorization: \`Bearer \${token}\`,
    };
  }

  // Remove authorization header
  clearAuth(): void {
    if (this.config.headers?.Authorization) {
      delete this.config.headers.Authorization;
    }
  }

  // Update base URL
  setBaseUrl(baseUrl: string): void {
    this.config.baseUrl = baseUrl;
  }

  // Get current configuration
  getConfig(): ApiClientConfig {
    return { ...this.config };
  }
}

// Typed API methods using generated types
export class TypedApiClient extends ApiClient {
  // Health check
  async getHealth() {
    return this.get<components["schemas"]["HealthCheck"]>('/health');
  }

  // API index
  async getApiIndex() {
    return this.get<components["schemas"]["ApiIndex"]>('/');
  }

  // Auth endpoints
  async signUp(data: components["schemas"]["SignUpRequest"]) {
    return this.post<components["schemas"]["SignUpResponse"]>(
      '/v1/auth/signup',
      data
    );
  }

  async signIn(data: components["schemas"]["SignInRequest"]) {
    return this.post<components["schemas"]["SignInResponse"]>(
      '/v1/auth/signin',
      data
    );
  }

  async signOut(data: components["schemas"]["SignOutRequest"]) {
    return this.post<components["schemas"]["SignOutResponse"]>(
      '/v1/auth/signout',
      data
    );
  }

  async refreshToken(data: components["schemas"]["RefreshRequest"]) {
    return this.post<components["schemas"]["RefreshResponse"]>(
      '/v1/auth/refresh',
      data
    );
  }

  // Profile endpoints
  async getUserProfile() {
    return this.get<components["schemas"]["ProfileResponse"]>(
      '/v1/profiles/me'
    );
  }

  async updateUserProfile(data: components["schemas"]["UpdateProfileRequest"]) {
    return this.put<components["schemas"]["UpdateProfileResponse"]>(
      '/v1/profiles/me',
      data
    );
  }

  // Listings endpoints
  async getListings(params?: Record<string, string | number>) {
    const searchParams = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : '';
    return this.get<components["schemas"]["ListingListResponse"]>(
      \`/v1/listings\${searchParams}\`
    );
  }

  async getListing(id: string) {
    return this.get<components["schemas"]["ListingResponse"]>(
      \`/v1/listings/\${id}\`
    );
  }

  async createListing(data: components["schemas"]["CreateListingRequest"]) {
    return this.post<components["schemas"]["ListingResponse"]>(
      '/v1/listings',
      data
    );
  }

  async updateListing(
    id: string,
    data: components["schemas"]["UpdateListingRequest"]
  ) {
    return this.put<components["schemas"]["ListingResponse"]>(
      \`/v1/listings/\${id}\`,
      data
    );
  }

  async deleteListing(id: string) {
    return this.delete<{ success: boolean; message: string }>(
      \`/v1/listings/\${id}\`
    );
  }

  // File endpoints
  async uploadFile(data: FormData) {
    return this.request<components["schemas"]["UploadFileResponse"]>(
      '/v1/files/upload',
      {
        method: 'POST',
        body: data,
        // Don't set Content-Type for FormData, let browser set it with boundary
        headers: Object.fromEntries(
          Object.entries(this.config.headers || {}).filter(([key]) => key.toLowerCase() !== 'content-type')
        ),
      }
    );
  }

  async getFile(id: string) {
    return this.get<components["schemas"]["GetFileResponse"]>(
      \`/v1/files/\${id}\`
    );
  }

  async deleteFile(id: string) {
    return this.delete<components["schemas"]["DeleteFileResponse"]>(
      \`/v1/files/\${id}\`
    );
  }
}

// Default export - create a singleton instance
export const apiClient = new TypedApiClient();

// Also export the class for custom instances
export default TypedApiClient;`;
}

const router = createRouter()
  .openapi(
    createRoute({
      tags: ["API Client"],
      method: "get",
      path: "/apiClient",
      summary: "Get API Client",
      description: "Returns the generated API client with TypeScript types and usage examples",
      responses: {
        [HttpStatusCodes.OK]: {
          content: {
            "application/json": {
              schema: apiClientResponseSchema,
            },
          },
          description: "API Client Information",
        },
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: {
          content: {
            "application/json": {
              schema: z.object({
                error: z.string(),
                message: z.string(),
              }).openapi("ApiClientError"),
            },
          },
          description: "Internal server error",
        },
      },
    }),
    (c) => {
      try {
        // Generate API client dynamically
        const apiClientCode = generateApiClientCode();
        
        // Read the generated types
        const typesPath = join(process.cwd(), 'src', 'types', 'api.ts');
        const types = readFileSync(typesPath, 'utf-8');

        return c.json({
          version: "1.0.0",
          client: apiClientCode,
          types: types,
          usage: {
            installation: `// Save the client code as api-client.ts
// Save the types as api-types.ts
// Install dependencies
npm install

// Or use the generated files directly`,
            
            basicUsage: `import { apiClient, TypedApiClient } from './api-client';

// Use the default instance
const response = await apiClient.getHealth();
console.log(response.data);

// Or create a custom instance
const customClient = new TypedApiClient({
  baseUrl: 'https://your-api-domain.com',
  timeout: 15000,
});`,
            
            authentication: `// Set authentication token
apiClient.setAuth('your-jwt-token-here');

// Make authenticated requests
const profile = await apiClient.getUserProfile();

// Clear authentication
apiClient.clearAuth();`,
            
            examples: [
              {
                title: "Authentication",
                code: `// Sign up
const signUpData = {
  email: "<EMAIL>",
  password: "securePassword123",
  confirmPassword: "securePassword123",
  first_name: "John",
  last_name: "Doe",
  company_name: "Acme Corp",
  company_type: "individual",
  terms_accepted: true
};

const signUpResponse = await apiClient.signUp(signUpData);
console.log('User created:', signUpResponse.data);

// Sign in
const signInResponse = await apiClient.signIn({
  email: "<EMAIL>",
  password: "securePassword123"
});

// Set the token for future requests
apiClient.setAuth(signInResponse.data.access_token);`
              },
              {
                title: "Working with Listings",
                code: `// Get all listings with pagination and filters
const listingsResponse = await apiClient.getListings({
  page: 1,
  limit: 20,
  status: 'active',
  min_price: 50000,
  max_price: 500000
});

console.log('Listings:', listingsResponse.data);

// Create a new listing
const newListing = {
  business_name: "Coffee Shop Downtown",
  industry: "Food & Beverage",
  asking_price: 150000,
  annual_revenue: 200000,
  cash_flow_sde: 45000,
  general_location: "Downtown Seattle",
  year_established: 2015,
  employees: 5,
  team_visibility: "all"
};

const createResponse = await apiClient.createListing(newListing);
console.log('Created listing:', createResponse.data);

// Get a specific listing
const listingId = createResponse.data.listing.id;
const listingResponse = await apiClient.getListing(listingId);
console.log('Listing details:', listingResponse.data);`
              },
              {
                title: "File Upload",
                code: `// Upload a file
const fileInput = document.getElementById('file-input') as HTMLInputElement;
const file = fileInput.files[0];

const formData = new FormData();
formData.append('file', file);
formData.append('file_type', 'document');
formData.append('entity_type', 'listing');
formData.append('entity_id', listingId);

const uploadResponse = await apiClient.uploadFile(formData);
console.log('File uploaded:', uploadResponse.data);

// Get file details
const fileResponse = await apiClient.getFile(uploadResponse.data.file.id);
console.log('File details:', fileResponse.data);`
              },
              {
                title: "Error Handling",
                code: `try {
  const response = await apiClient.getUserProfile();
  console.log('Profile:', response.data);
} catch (error) {
  if (error instanceof Error) {
    console.error('API Error:', error.message);
    
    // Handle specific error cases
    if (error.message.includes('401')) {
      // Unauthorized - token expired or invalid
      apiClient.clearAuth();
      // Redirect to login or refresh token
    } else if (error.message.includes('404')) {
      // Resource not found
      console.log('Profile not found');
    } else if (error.message.includes('timeout')) {
      // Request timeout
      console.log('Request timed out, please try again');
    }
  }
}`
              },
              {
                title: "Custom Configuration",
                code: `// Create a client with custom configuration
const productionClient = new TypedApiClient({
  baseUrl: 'https://api.yourcompany.com',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Version': '1.0',
    'X-Client-Version': '2.1.0'
  }
});

// Update configuration at runtime
productionClient.setBaseUrl('https://staging-api.yourcompany.com');
productionClient.setAuth('new-token');

// Get current configuration
const config = productionClient.getConfig();
console.log('Current config:', config);`
              }
            ]
          }
        }, HttpStatusCodes.OK);
      } catch (error) {
        return c.json({
          error: "Failed to generate API client",
          message: error instanceof Error ? error.message : "Unknown error"
        }, HttpStatusCodes.INTERNAL_SERVER_ERROR);
      }
    }
  );

export default router; 