import db from "@/db";
import { member, userProfiles } from "@/db/schema";
import { eq, and, sql } from "drizzle-orm";

interface MinimalUser {
  id: string;
  name?: string | null;
  email?: string | null;
}

import { createAuthMiddleware } from "better-auth/api";

/**
 * After-hook to include session object in responses that return a session token.
 * Applied to email sign-in and sign-up responses when a token is present.
 */
export const includeSessionInAuthResponse = createAuthMiddleware(async (ctx) => {
  const path = ctx.path;
  if (path !== "/sign-in/email" && path !== "/sign-up/email") return;
  if (!ctx.context.returned || typeof ctx.context.returned !== "object") return;
  try {
    const ret = ctx.context.returned as { token?: string } & Record<string, unknown>;

    // Prefer the just-created session from context to avoid an extra DB read
    if (ctx.context.newSession) {
      return { response: { ...ret, session: ctx.context.newSession } };
    }

    // Fallback: if a token is present but newSession is missing, resolve session once
    const token = (ret as any)?.token;
    if (token) {
      const found = await ctx.context.internalAdapter.findSession(token);
      if (found?.session) {
        return { response: { ...ret, session: found.session } };
      }
    }
  } catch {
    // non-fatal
  }
});




// Create organization via Better Auth API, and create user profile for the new user
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function provisionUserTenant(authInstance: any, newUser: MinimalUser): Promise<string | undefined> {
  if (!newUser?.id) return undefined;

  const baseName =
    (newUser?.name || undefined) ??
    (typeof newUser?.email === "string" ? newUser.email.split("@")[0] : "My Organization");

  const metadata = { plan: "trial", status: "trial" };

  // Use Better Auth built-in API to create organization and membership
  // Prefer returning id from Better Auth instead of DB lookup
  // Response shape can vary across versions, so access defensively
  const createdOrg: any = await authInstance.api.createOrganization({
    body: {
      name: baseName || "My Organization",
      metadata,
      userId: newUser.id,
      keepCurrentActiveOrganization: false,
    },
  });

  const organizationId: string | undefined = createdOrg?.data?.id ?? createdOrg?.id ?? createdOrg?.organization?.id;

  if (organizationId) {
    // Create user profile linked to this organization
    await db
      .insert(userProfiles)
      .values({
        userId: newUser.id,
        organizationId,
        isActive: true,
        joinedAt: new Date().toISOString(),
        displayName: (newUser?.name || undefined) ?? undefined,
      })
      // Skip duplicate profile for the same (organizationId, userId)
      .onConflictDoNothing({
        target: [userProfiles.organizationId, userProfiles.userId],
      });
  }

  return organizationId;
}

// Ensure session payload has an activeOrganizationId set based on membership
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function ensureActiveOrganizationOnSession(payload: any): Promise<void> {
  const userId: string | undefined = payload?.session?.userId ?? payload?.user?.id ?? payload?.userId;
  if (!userId) return;

  if (payload?.session?.activeOrganizationId) return;

  // Single round-trip: pick an owner org if available, otherwise any membership
  const row = await db
    .select({ organizationId: member.organizationId })
    .from(member)
    .where(eq(member.userId, userId))
    .orderBy(sql`CASE WHEN ${member.role} = 'owner' THEN 0 ELSE 1 END`)
    .limit(1);

  const organizationId = row?.[0]?.organizationId;
  if (organizationId && payload.session) {
    payload.session.activeOrganizationId = organizationId;
  }
}


