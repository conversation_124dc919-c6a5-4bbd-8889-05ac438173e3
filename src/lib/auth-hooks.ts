import db from "@/db";
import { member, userProfiles, organization, session } from "@/db/schema";
import { eq, and, sql } from "drizzle-orm";
import { randomUUID } from "crypto";

interface MinimalUser {
  id: string;
  name?: string | null;
  email?: string | null;
}

import { createAuthMiddleware } from "better-auth/api";

/**
 * After-hook to include session object in responses that return a session token.
 * Applied to email sign-in and sign-up responses when a token is present.
 */
export const includeSessionInAuthResponse = createAuthMiddleware(async (ctx) => {
  const path = ctx.path;
  if (path !== "/sign-in/email" && path !== "/sign-up/email") return;
  if (!ctx.context.returned || typeof ctx.context.returned !== "object") return;
  try {
    const ret = ctx.context.returned as { token?: string } & Record<string, unknown>;

    // Prefer the just-created session from context to avoid an extra DB read
    if (ctx.context.newSession) {
      return { response: { ...ret, session: ctx.context.newSession } };
    }

    // Fallback: if a token is present but newSession is missing, resolve session once
    const token = (ret as any)?.token;
    if (token) {
      const found = await ctx.context.internalAdapter.findSession(token);
      if (found?.session) {
        return { response: { ...ret, session: found.session } };
      }
    }
  } catch {
    // non-fatal
  }
});




// Create organization via Better Auth API, and create user profile for the new user
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function provisionUserTenant(authInstance: any, newUser: MinimalUser): Promise<string | undefined> {
  console.log('Debug - provisionUserTenant called with user:', newUser);

  if (!newUser?.id) {
    console.log('Debug - No user ID provided, skipping provisioning');
    return undefined;
  }

  const baseName =
    (newUser?.name || undefined) ??
    (typeof newUser?.email === "string" ? newUser.email.split("@")[0] : "My Organization");

  // Generate a slug from the name
  const slug = baseName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');

  console.log('Debug - Creating organization with name:', baseName, 'and slug:', slug);

  const metadata = { plan: "trial", status: "trial" };

  // Create organization directly in database instead of using Better Auth API
  // which seems to have validation issues
  const organizationId = randomUUID();

  try {
    // Insert organization directly into database
    await db.insert(organization).values({
      id: organizationId,
      name: baseName || "My Organization",
      slug: slug || "my-organization",
      metadata: JSON.stringify(metadata),
      createdAt: new Date(),
    });

    console.log('Debug - Created organization directly in DB with ID:', organizationId);

    // Create membership for the user as owner
    await db.insert(member).values({
      id: randomUUID(),
      organizationId,
      userId: newUser.id,
      role: 'owner',
      createdAt: new Date(),
    });

    console.log('Debug - Created membership for user as owner');
  } catch (error) {
    console.log('Debug - Error creating organization or membership:', error);
    return undefined;
  }

  if (organizationId) {
    console.log('Debug - Creating user profile for user:', newUser.id, 'in org:', organizationId);

    // Create user profile linked to this organization
    await db
      .insert(userProfiles)
      .values({
        userId: newUser.id,
        organizationId,
        isActive: true,
        joinedAt: new Date().toISOString(),
        displayName: (newUser?.name || undefined) ?? undefined,
      })
      // Skip duplicate profile for the same (organizationId, userId)
      .onConflictDoNothing({
        target: [userProfiles.organizationId, userProfiles.userId],
      });

    console.log('Debug - User profile created successfully');
  } else {
    console.log('Debug - No organization ID, skipping user profile creation');
  }

  return organizationId;
}

// Ensure session payload has an activeOrganizationId set based on membership
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function ensureActiveOrganizationOnSession(payload: any): Promise<void> {
  console.log('Debug - ensureActiveOrganizationOnSession called with payload:', payload);

  const userId: string | undefined = payload?.session?.userId ?? payload?.user?.id ?? payload?.userId;
  console.log('Debug - Extracted user ID:', userId);

  if (!userId) {
    console.log('Debug - No user ID found, skipping organization assignment');
    return;
  }

  if (payload?.activeOrganizationId) {
    console.log('Debug - Session already has activeOrganizationId:', payload.activeOrganizationId);
    return;
  }

  console.log('Debug - Looking up organization membership for user:', userId);

  // Single round-trip: pick an owner org if available, otherwise any membership
  const row = await db
    .select({ organizationId: member.organizationId })
    .from(member)
    .where(eq(member.userId, userId))
    .orderBy(sql`CASE WHEN ${member.role} = 'owner' THEN 0 ELSE 1 END`)
    .limit(1);

  console.log('Debug - Organization membership query result:', row);

  const organizationId = row?.[0]?.organizationId;
  console.log('Debug - Found organization ID:', organizationId);

  if (organizationId) {
    // Update the session in the database directly
    try {
      await db
        .update(session)
        .set({ activeOrganizationId: organizationId })
        .where(eq(session.id, payload.id));

      console.log('Debug - Updated session in database with activeOrganizationId:', organizationId);
    } catch (error) {
      console.log('Debug - Error updating session in database:', error);
    }
  } else {
    console.log('Debug - No organization found to set on session');
  }
}


