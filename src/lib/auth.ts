import { betterAuth } from "better-auth";
import { admin, organization, openAPI } from "better-auth/plugins";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import db from "@/db";
import env from "@/env";
import { provisionUserTenant, ensureActiveOrganizationOnSession, includeSessionInAuthResponse } from "@/lib/auth-hooks";

// Minimal Better Auth server instance. It will expose handlers we can mount under /api/auth.
export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
  }),
  baseURL: process.env.BETTER_AUTH_URL || `http://localhost:${env.PORT}`,
  secret: process.env.BETTER_AUTH_SECRET || "dev-secret-change-me",
  // Enable email/password auth as per server usage guide
  emailAndPassword: { enabled: true, autoSignIn: true },
  // Allow requests from these origins to interact with Better Auth (e.g. cookies, redirects)
  trustedOrigins: env.CORS_ORIGINS,
  // Cache session payload in a signed cookie to avoid DB reads on session checks
  session: {
    cookieCache: { enabled: true, maxAge: 5 * 60 },
  },
  plugins: [admin(), organization(), openAPI()],
  // Augment responses
  hooks: {
    // Reuse shared after-hook for both sign-in and sign-up email flows
    after: includeSessionInAuthResponse,
  },
  // Hooks to mirror legacy behavior: after user creation, create org, member(owner), and profile
  // Casting to any to allow forward-compat hooks without tight coupling to library types
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  databaseHooks: {
    user: {
      create: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        after: async (payload: any) => {
          try {
            const newUser = payload?.user ?? payload;
            await provisionUserTenant(auth, newUser);
          } catch {
            // Best-effort hook; do not block auth flow on ancillary record creation
          }
        },
      },
    },
    session: {
      create: {
        // Ensure a default active organization is set on session creation
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        before: async (payload: any) => {
          try {
            await ensureActiveOrganizationOnSession(payload);
          } catch {
            // Non-fatal
          }
        },
      },
    },
  } as any,
});

export type BetterAuth = typeof auth;


